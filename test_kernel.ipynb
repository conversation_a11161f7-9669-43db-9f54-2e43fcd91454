{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simple test cell\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "print('Hello! Python is working!')\n", "print('NumPy version:', np.__version__)\n", "print('Matplotlib version:', plt.matplotlib.__version__)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.14", "language": "python", "name": "python314"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.14.0"}}, "nbformat": 4, "nbformat_minor": 4}